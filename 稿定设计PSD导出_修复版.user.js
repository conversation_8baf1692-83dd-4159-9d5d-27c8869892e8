// ==UserScript==
// @name         稿定设计PSD导出工具 - 修复版
// @namespace    http://tampermonkey.net/
// @version      2.1
// @description  为稿定设计添加真正的PSD文件导出功能，修复兼容性问题
// <AUTHOR>
// @match        https://www.gaoding.com/editor/*
// @match        https://gaoding.com/editor/*
// @grant        none
// @require      https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js
// @require      https://unpkg.com/ag-psd@14.3.11/dist/bundle.js
// ==/UserScript==

(function() {
    'use strict';

    let isInitialized = false;

    // 等待元素出现
    function waitForElement(selector, timeout = 15000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            function check() {
                const element = document.querySelector(selector);
                if (element) {
                    resolve(element);
                } else if (Date.now() - startTime > timeout) {
                    reject(new Error(`未找到元素: ${selector}`));
                } else {
                    setTimeout(check, 200);
                }
            }
            check();
        });
    }

    // 显示消息提示
    function showMessage(message, type = 'info', duration = 4000) {
        const messageDiv = document.createElement('div');
        messageDiv.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 10px;
            color: white;
            font-size: 14px;
            z-index: 100000;
            max-width: 150px;
            word-wrap: break-word;
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
            backdrop-filter: blur(10px);
            ${type === 'success' ? 'background: linear-gradient(135deg, #52c41a, #73d13d);' : 
              type === 'error' ? 'background: linear-gradient(135deg, #ff4d4f, #ff7875);' : 
              'background: linear-gradient(135deg, #1890ff, #40a9ff);'}
        `;
        messageDiv.innerHTML = message.replace(/\n/g, '<br>');
        document.body.appendChild(messageDiv);

        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.style.opacity = '0';
                messageDiv.style.transform = 'translateX(100%)';
                messageDiv.style.transition = 'all 0.3s ease';
                setTimeout(() => messageDiv.remove(), 300);
            }
        }, duration);
    }

    // 获取最佳画布
    function getBestCanvas() {
        const canvases = Array.from(document.querySelectorAll('canvas'));
        console.log('找到画布数量:', canvases.length);
        
        // 过滤掉太小的画布
        const validCanvases = canvases.filter(canvas => 
            canvas.width > 100 && canvas.height > 100
        );

        if (validCanvases.length === 0) {
            throw new Error('未找到有效的画布');
        }

        // 选择最大的画布
        const bestCanvas = validCanvases.reduce((largest, current) => {
            const largestArea = largest.width * largest.height;
            const currentArea = current.width * current.height;
            return currentArea > largestArea ? current : largest;
        });

        console.log('选择的画布:', bestCanvas.className, '尺寸:', bestCanvas.width, 'x', bestCanvas.height);
        return bestCanvas;
    }

    // 简化的PSD生成器
    class SimplePSDGenerator {
        constructor() {
            this.layers = [];
            this.width = 0;
            this.height = 0;
        }

        setCanvasSize(width, height) {
            this.width = width;
            this.height = height;
        }

        addLayer(name, canvas, x = 0, y = 0, opacity = 1) {
            // 验证canvas
            if (!canvas || typeof canvas.getContext !== 'function') {
                console.warn('无效的canvas对象:', name);
                return;
            }

            // 获取canvas的ImageData
            const ctx = canvas.getContext('2d');
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

            this.layers.push({
                name: name,
                canvas: canvas,
                imageData: imageData,
                left: Math.round(x),
                top: Math.round(y),
                right: Math.round(x + canvas.width),
                bottom: Math.round(y + canvas.height),
                opacity: Math.round(opacity * 255),
                blendMode: 'normal'
            });

            console.log(`添加图层: ${name}, 尺寸: ${canvas.width}x${canvas.height}, 位置: (${x}, ${y})`);
        }

        async generatePSD() {
            if (!window.agPsd) {
                throw new Error('PSD库未加载完成，请刷新页面重试');
            }

            if (this.layers.length === 0) {
                throw new Error('没有可导出的图层');
            }

            try {
                console.log('开始生成PSD，图层数量:', this.layers.length);

                // 创建主画布作为合成背景
                const mainCanvas = document.createElement('canvas');
                mainCanvas.width = this.width;
                mainCanvas.height = this.height;
                const mainCtx = mainCanvas.getContext('2d');

                // 填充白色背景
                mainCtx.fillStyle = '#FFFFFF';
                mainCtx.fillRect(0, 0, this.width, this.height);

                // 将所有图层合成到主画布上
                for (let i = 0; i < this.layers.length; i++) {
                    const layer = this.layers[i];
                    if (layer.canvas && layer.canvas.width > 0 && layer.canvas.height > 0) {
                        mainCtx.globalAlpha = layer.opacity / 255;
                        mainCtx.drawImage(layer.canvas, layer.left, layer.top);
                        mainCtx.globalAlpha = 1.0;
                    }
                }

                // 创建符合ag-psd格式的PSD文档结构
                const psd = {
                    width: this.width,
                    height: this.height,
                    channels: 3,
                    bitsPerChannel: 8,
                    colorMode: 3, // RGB
                    canvas: mainCanvas,
                    children: []
                };

                // 添加图层到PSD结构
                for (let i = 0; i < this.layers.length; i++) {
                    const layer = this.layers[i];

                    if (!layer.canvas || layer.canvas.width <= 0 || layer.canvas.height <= 0) {
                        console.warn(`跳过无效图层: ${layer.name}`);
                        continue;
                    }

                    console.log(`添加图层到PSD: ${layer.name}`);

                    // 创建符合ag-psd格式的图层对象
                    const psdLayer = {
                        name: layer.name,
                        opacity: layer.opacity,
                        blendMode: 'normal',
                        left: layer.left,
                        top: layer.top,
                        right: layer.right,
                        bottom: layer.bottom,
                        canvas: layer.canvas
                    };

                    psd.children.push(psdLayer);
                }

                // 确保至少有一个图层
                if (psd.children.length === 0) {
                    console.log('没有有效图层，添加背景图层');
                    psd.children.push({
                        name: '背景',
                        opacity: 255,
                        blendMode: 'normal',
                        left: 0,
                        top: 0,
                        right: this.width,
                        bottom: this.height,
                        canvas: mainCanvas
                    });
                }

                console.log('PSD结构完成:', {
                    width: psd.width,
                    height: psd.height,
                    layerCount: psd.children.length,
                    layers: psd.children.map(l => ({
                        name: l.name,
                        size: `${l.right - l.left}x${l.bottom - l.top}`,
                        position: `(${l.left}, ${l.top})`
                    }))
                });

                // 生成PSD文件
                console.log('开始写入PSD文件...');
                const arrayBuffer = window.agPsd.writePsd(psd);
                console.log('PSD文件生成完成，大小:', arrayBuffer.byteLength, 'bytes');

                return new Blob([arrayBuffer], { type: 'application/octet-stream' });

            } catch (error) {
                console.error('PSD生成详细错误:', error);
                console.error('错误堆栈:', error.stack);
                throw new Error(`PSD生成失败: ${error.message}`);
            }
        }
    }

    // 导出PNG
    async function exportPNG() {
        try {
            showMessage('正在生成PNG图片...', 'info');
            
            const canvas = getBestCanvas();
            
            const link = document.createElement('a');
            const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
            link.download = `稿定设计_${timestamp}.png`;
            link.href = canvas.toDataURL('image/png', 1.0);
            link.click();
            
            showMessage('✅ PNG导出成功！', 'success');
        } catch (error) {
            console.error('PNG导出失败:', error);
            showMessage('❌ PNG导出失败: ' + error.message, 'error');
        }
    }

    // 导出PSD文件
    async function exportPSD() {
        try {
            showMessage('正在分析页面结构...\n这可能需要几秒钟时间', 'info', 6000);
            
            const canvas = getBestCanvas();
            const psdGen = new SimplePSDGenerator();
            psdGen.setCanvasSize(canvas.width, canvas.height);

            // 添加主画布作为背景
            const backgroundCanvas = document.createElement('canvas');
            backgroundCanvas.width = canvas.width;
            backgroundCanvas.height = canvas.height;
            const bgCtx = backgroundCanvas.getContext('2d');
            
            // 复制主画布内容
            bgCtx.drawImage(canvas, 0, 0);
            psdGen.addLayer('背景', backgroundCanvas, 0, 0, 1);

            let layerCount = 1;

            // 尝试从稿定设计的特定结构提取图层
            const extractedCount = await extractGaodingLayers(psdGen);
            layerCount += extractedCount;

            // 如果没有提取到额外图层，尝试通用方法
            if (psdGen.layers.length === 1) {
                console.log('使用通用方法提取图层');

                // 尝试查找稿定设计的特定元素
                await tryExtractGaodingElements(psdGen, canvas);
                
                // 方法1: 提取可见的图片元素
                const images = document.querySelectorAll('img[src]');
                console.log('找到图片元素:', images.length);
                
                for (let i = 0; i < Math.min(images.length, 10); i++) {
                    const img = images[i];
                    const rect = img.getBoundingClientRect();
                    const canvasRect = canvas.getBoundingClientRect();
                    
                    // 检查图片是否在画布区域内
                    if (rect.width > 30 && rect.height > 30 && 
                        rect.left >= canvasRect.left && rect.top >= canvasRect.top &&
                        rect.right <= canvasRect.right && rect.bottom <= canvasRect.bottom) {
                        
                        try {
                            // 跳过尺寸为0的canvas
                            if ((img.naturalWidth || rect.width) === 0 || (img.naturalHeight || rect.height) === 0) {
                                continue;
                            }

                            const imgCanvas = document.createElement('canvas');
                            imgCanvas.width = img.naturalWidth || rect.width;
                            imgCanvas.height = img.naturalHeight || rect.height;
                            const imgCtx = imgCanvas.getContext('2d');

                            if (img.complete && img.naturalWidth > 0) {
                                // 设置crossOrigin以避免污染
                                if (!img.crossOrigin) {
                                    img.crossOrigin = 'anonymous';
                                }

                                try {
                                    imgCtx.drawImage(img, 0, 0, imgCanvas.width, imgCanvas.height);

                                    // 测试canvas是否被污染
                                    imgCtx.getImageData(0, 0, 1, 1);

                                    // 计算相对于画布的位置
                                    const relativeX = rect.left - canvasRect.left;
                                    const relativeY = rect.top - canvasRect.top;

                                    psdGen.addLayer(`图片${layerCount}`, imgCanvas, relativeX, relativeY, 1);
                                    layerCount++;
                                } catch (taintError) {
                                    console.warn('图片被污染，跳过:', img.src);
                                    // 尝试使用html2canvas截取图片元素
                                    try {
                                        const imgElementCanvas = await html2canvas(img, {
                                            useCORS: true,
                                            allowTaint: false,
                                            backgroundColor: null,
                                            scale: 1
                                        });

                                        if (imgElementCanvas.width > 0 && imgElementCanvas.height > 0) {
                                            const relativeX = rect.left - canvasRect.left;
                                            const relativeY = rect.top - canvasRect.top;
                                            psdGen.addLayer(`图片${layerCount}`, imgElementCanvas, relativeX, relativeY, 1);
                                            layerCount++;
                                        }
                                    } catch (html2canvasError) {
                                        console.warn('html2canvas也失败:', html2canvasError);
                                    }
                                }
                            }
                        } catch (e) {
                            console.warn('图片处理失败:', e);
                        }
                    }
                }

                // 方法2: 使用html2canvas截取特定区域
                const designArea = document.querySelector('.infinite-canvas')?.parentElement;
                if (designArea && layerCount < 5) {
                    try {
                        const areaCanvas = await html2canvas(designArea, {
                            backgroundColor: null,
                            scale: 0.5,
                            useCORS: true,
                            allowTaint: false, // 避免污染
                            logging: false,
                            ignoreElements: (element) => {
                                // 忽略可能导致污染的元素
                                return element.tagName === 'IFRAME' ||
                                       element.tagName === 'VIDEO' ||
                                       (element.tagName === 'CANVAS' && element.width === 0);
                            }
                        });

                        if (areaCanvas.width > 0 && areaCanvas.height > 0) {
                            psdGen.addLayer(`设计区域`, areaCanvas, 0, 0, 0.8);
                            layerCount++;
                        }
                    } catch (e) {
                        console.warn('设计区域截取失败:', e);
                    }
                }
            }

            showMessage(`正在生成PSD文件...\n当前有 ${psdGen.layers.length} 个图层`, 'info', 8000);

            // 验证图层
            console.log('最终图层列表:');
            psdGen.layers.forEach((layer, index) => {
                console.log(`图层 ${index + 1}: ${layer.name}, 尺寸: ${layer.canvas.width}x${layer.canvas.height}`);
            });

            // 生成PSD
            const psdBlob = await psdGen.generatePSD();
            
            // 下载文件
            const link = document.createElement('a');
            const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
            link.download = `稿定设计_${timestamp}.psd`;
            link.href = URL.createObjectURL(psdBlob);
            link.click();
            
            showMessage(`✅ PSD文件导出成功！\n包含 ${psdGen.layers.length} 个图层\n可在Photoshop中打开编辑`, 'success', 5000);
            
        } catch (error) {
            console.error('PSD导出失败:', error);
            showMessage('❌ PSD导出失败:\n' + error.message, 'error', 6000);
        }
    }

    // 从稿定设计画板提取所有设计图层
    async function extractGaodingLayers(psdGen) {
        console.log('开始提取稿定设计图层...');

        // 方法1: 查找画板容器
        let canvas = document.querySelector('.infinite-canvas');
        if (!canvas) {
            // 方法2: 查找其他可能的画布
            canvas = document.querySelector('canvas[width][height]');
        }
        if (!canvas) {
            console.warn('未找到画板');
            return 0;
        }

        const canvasRect = canvas.getBoundingClientRect();
        let extractedLayers = 0;

        console.log('画板信息:', {
            size: `${canvas.width}x${canvas.height}`,
            position: `${canvasRect.left}, ${canvasRect.top}`
        });

        // 查找所有可能的图层元素
        const selectors = [
            'img[src]',                    // 图片
            'canvas',                      // 画布
            'svg',                         // SVG
            '[style*="background-image"]', // 背景图片
            '.text-element',               // 文本元素
            '.image-element',              // 图片元素
            '.shape-element',              // 形状元素
            '[data-type]',                 // 带数据类型的元素
            '.layer',                      // 图层类
            '.element'                     // 通用元素类
        ];

        const allElements = [];
        selectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => {
                if (!allElements.includes(el)) {
                    allElements.push(el);
                }
            });
        });

        console.log(`找到 ${allElements.length} 个潜在图层元素`);

        // 过滤和处理元素
        for (let i = 0; i < Math.min(allElements.length, 20); i++) {
            const element = allElements[i];
            const rect = element.getBoundingClientRect();

            // 跳过无效元素
            if (rect.width < 5 || rect.height < 5 || element === canvas) {
                continue;
            }

            // 检查是否在画板区域内或附近
            const isInCanvas = (
                rect.left >= canvasRect.left - 50 &&
                rect.top >= canvasRect.top - 50 &&
                rect.right <= canvasRect.right + 50 &&
                rect.bottom <= canvasRect.bottom + 50
            );

            if (!isInCanvas) {
                continue;
            }

            try {
                console.log(`处理元素 ${i + 1}:`, {
                    tag: element.tagName,
                    size: `${Math.round(rect.width)}x${Math.round(rect.height)}`,
                    position: `${Math.round(rect.left)}, ${Math.round(rect.top)}`
                });

                // 创建元素的canvas
                let elementCanvas;

                if (element.tagName === 'CANVAS') {
                    // 直接复制canvas
                    elementCanvas = document.createElement('canvas');
                    elementCanvas.width = element.width;
                    elementCanvas.height = element.height;
                    const ctx = elementCanvas.getContext('2d');
                    ctx.drawImage(element, 0, 0);
                } else {
                    // 使用html2canvas渲染
                    elementCanvas = await html2canvas(element, {
                        backgroundColor: null,
                        scale: 1,
                        useCORS: true,
                        allowTaint: true,
                        logging: false,
                        width: Math.round(rect.width),
                        height: Math.round(rect.height)
                    });
                }

                // 验证canvas内容
                if (elementCanvas && elementCanvas.width > 0 && elementCanvas.height > 0) {
                    const ctx = elementCanvas.getContext('2d');
                    const imageData = ctx.getImageData(0, 0, elementCanvas.width, elementCanvas.height);

                    // 检查是否有非透明像素
                    let hasContent = false;
                    for (let j = 3; j < imageData.data.length; j += 4) {
                        if (imageData.data[j] > 0) {
                            hasContent = true;
                            break;
                        }
                    }

                    if (hasContent) {
                        const relativeX = Math.max(0, Math.round(rect.left - canvasRect.left));
                        const relativeY = Math.max(0, Math.round(rect.top - canvasRect.top));

                        let layerName = `图层${extractedLayers + 1}`;
                        if (element.tagName === 'IMG') layerName = `图片${extractedLayers + 1}`;
                        else if (element.tagName === 'CANVAS') layerName = `画布${extractedLayers + 1}`;
                        else if (element.tagName === 'SVG') layerName = `矢量${extractedLayers + 1}`;

                        psdGen.addLayer(layerName, elementCanvas, relativeX, relativeY, 1);
                        extractedLayers++;
                        console.log(`${layerName} 添加成功`);
                    }
                }
            } catch (e) {
                console.warn(`元素 ${i + 1} 处理失败:`, e.message);
            }
        }

        console.log(`成功提取 ${extractedLayers} 个图层`);
        return extractedLayers;
    }

    // 提取画板区域内的所有设计元素
    async function tryExtractGaodingElements(psdGen, mainCanvas) {
        const canvasRect = mainCanvas.getBoundingClientRect();
        let layerCount = 0;

        console.log('画板区域:', {
            left: canvasRect.left,
            top: canvasRect.top,
            width: canvasRect.width,
            height: canvasRect.height
        });

        // 查找画板容器
        const canvasContainer = mainCanvas.parentElement;
        if (!canvasContainer) {
            console.warn('未找到画板容器');
            return 0;
        }

        // 查找画板区域内的所有子元素
        const allElements = canvasContainer.querySelectorAll('*');
        console.log('画板容器内总元素数:', allElements.length);

        // 过滤出可能的设计元素
        const designElements = Array.from(allElements).filter(element => {
            const rect = element.getBoundingClientRect();
            const style = window.getComputedStyle(element);

            // 过滤条件
            return (
                rect.width > 5 && rect.height > 5 && // 有实际尺寸
                style.position === 'absolute' && // 绝对定位
                element !== mainCanvas && // 不是主画布
                !element.contains(mainCanvas) && // 不包含主画布
                rect.left >= canvasRect.left - 10 && // 在画板区域内
                rect.top >= canvasRect.top - 10 &&
                rect.right <= canvasRect.right + 10 &&
                rect.bottom <= canvasRect.bottom + 10 &&
                style.visibility !== 'hidden' && // 可见
                style.display !== 'none' &&
                parseFloat(style.opacity) > 0.1 // 不透明
            );
        });

        console.log('找到设计元素:', designElements.length);

        // 按z-index排序，确保图层顺序正确
        designElements.sort((a, b) => {
            const aZ = parseInt(window.getComputedStyle(a).zIndex) || 0;
            const bZ = parseInt(window.getComputedStyle(b).zIndex) || 0;
            return aZ - bZ;
        });

        // 提取每个设计元素
        for (let i = 0; i < Math.min(designElements.length, 20); i++) {
            const element = designElements[i];
            const rect = element.getBoundingClientRect();

            try {
                // 获取元素类型
                let elementType = 'unknown';
                if (element.tagName === 'IMG') elementType = 'image';
                else if (element.tagName === 'SVG') elementType = 'svg';
                else if (element.textContent && element.textContent.trim()) elementType = 'text';
                else if (element.style.backgroundColor || element.style.background) elementType = 'shape';

                console.log(`处理元素 ${i + 1}:`, {
                    tag: element.tagName,
                    type: elementType,
                    size: `${Math.round(rect.width)}x${Math.round(rect.height)}`,
                    position: `(${Math.round(rect.left - canvasRect.left)}, ${Math.round(rect.top - canvasRect.top)})`
                });

                // 使用html2canvas截取元素
                const elementCanvas = await html2canvas(element, {
                    backgroundColor: null,
                    scale: 1,
                    useCORS: false,
                    allowTaint: true, // 允许跨域内容
                    logging: false,
                    width: Math.round(rect.width),
                    height: Math.round(rect.height),
                    foreignObjectRendering: false,
                    removeContainer: true,
                    ignoreElements: (el) => {
                        // 忽略不需要的元素
                        return el.tagName === 'SCRIPT' ||
                               el.tagName === 'STYLE' ||
                               el.style.display === 'none';
                    }
                });

                if (elementCanvas.width > 0 && elementCanvas.height > 0) {
                    // 验证canvas是否有实际内容
                    const ctx = elementCanvas.getContext('2d');
                    const imageData = ctx.getImageData(0, 0, elementCanvas.width, elementCanvas.height);
                    const hasContent = imageData.data.some((value, index) => {
                        // 检查alpha通道，如果有非透明像素则认为有内容
                        return index % 4 === 3 && value > 0;
                    });

                    if (hasContent) {
                        const relativeX = Math.round(rect.left - canvasRect.left);
                        const relativeY = Math.round(rect.top - canvasRect.top);

                        // 生成有意义的图层名称
                        let layerName = `${elementType}_${layerCount + 1}`;
                        if (elementType === 'text' && element.textContent) {
                            const text = element.textContent.trim().substring(0, 10);
                            layerName = `文字_${text}`;
                        } else if (elementType === 'image' && element.alt) {
                            layerName = `图片_${element.alt.substring(0, 10)}`;
                        }

                        psdGen.addLayer(
                            layerName,
                            elementCanvas,
                            relativeX,
                            relativeY,
                            1
                        );
                        layerCount++;

                        console.log(`成功添加图层: ${layerName}, 内容验证通过`);
                    } else {
                        console.log(`跳过空白图层: ${elementType}_${layerCount + 1}`);
                    }
                }
            } catch (e) {
                console.warn(`元素 ${i + 1} 处理失败:`, e.message);
            }
        }

        console.log(`从画板区域提取了 ${layerCount} 个图层`);
        return layerCount;
    }

    // 创建导出按钮
    function createExportButtons() {
        if (document.getElementById('psd-export-panel-fixed')) {
            return;
        }

        const panel = document.createElement('div');
        panel.id = 'psd-export-panel-fixed';
        panel.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 99999;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 8px;
            padding: 8px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.3);
            width: 80px;
            cursor: move;
            user-select: none;
        `;

        // 添加拖拽功能
        let isDragging = false;
        let dragOffset = { x: 0, y: 0 };

        panel.addEventListener('mousedown', (e) => {
            isDragging = true;
            dragOffset.x = e.clientX - panel.offsetLeft;
            dragOffset.y = e.clientY - panel.offsetTop;
            panel.style.cursor = 'grabbing';
        });

        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                const x = e.clientX - dragOffset.x;
                const y = e.clientY - dragOffset.y;

                // 限制在窗口范围内
                const maxX = window.innerWidth - panel.offsetWidth;
                const maxY = window.innerHeight - panel.offsetHeight;

                panel.style.left = Math.max(0, Math.min(x, maxX)) + 'px';
                panel.style.top = Math.max(0, Math.min(y, maxY)) + 'px';
                panel.style.right = 'auto'; // 取消right定位
            }
        });

        document.addEventListener('mouseup', () => {
            if (isDragging) {
                isDragging = false;
                panel.style.cursor = 'move';
            }
        });

        const title = document.createElement('div');
        title.textContent = '🎨';
        title.style.cssText = `
            font-size: 18px;
            text-align: center;
            margin-bottom: 5px;
            cursor: move;
        `;

        const buttonStyle = `
            width: 100%;
            padding: 6px;
            margin-bottom: 4px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 11px;
            font-weight: 500;
            transition: all 0.2s ease;
            box-shadow: 0 1px 4px rgba(0,0,0,0.1);
        `;

        const pngBtn = document.createElement('button');
        pngBtn.textContent = 'PNG';
        pngBtn.style.cssText = buttonStyle + `
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        `;
        pngBtn.onclick = exportPNG;

        const psdBtn = document.createElement('button');
        psdBtn.textContent = 'PSD';
        psdBtn.style.cssText = buttonStyle + `
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        `;
        psdBtn.onclick = exportPSD;

        const testBtn = document.createElement('button');
        testBtn.textContent = '🔍 测试检测';
        testBtn.style.cssText = buttonStyle + `
            background: #6c757d;
            color: white;
            font-size: 12px;
            padding: 8px;
        `;
        testBtn.onclick = () => {
            try {
                const canvas = getBestCanvas();
                console.log('画布信息:', {
                    width: canvas.width,
                    height: canvas.height,
                    className: canvas.className
                });

                const images = document.querySelectorAll('img[src]');
                console.log('图片元素数量:', images.length);

                const elements = document.querySelectorAll('div[style*="position: absolute"]');
                console.log('绝对定位元素数量:', elements.length);

                showMessage(`检测结果:\n画布: ${canvas.width}x${canvas.height}\n图片: ${images.length}个\n元素: ${elements.length}个`, 'info', 5000);
            } catch (e) {
                showMessage('检测失败: ' + e.message, 'error');
            }
        };

        // 添加悬停效果
        [pngBtn, psdBtn].forEach(btn => {
            btn.addEventListener('mouseenter', () => {
                btn.style.transform = 'translateY(-3px)';
                btn.style.boxShadow = '0 6px 20px rgba(0,0,0,0.25)';
            });
            btn.addEventListener('mouseleave', () => {
                btn.style.transform = 'translateY(0)';
                btn.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
            });
        });

        panel.appendChild(title);
        panel.appendChild(pngBtn);
        panel.appendChild(psdBtn);
        panel.appendChild(testBtn);
        document.body.appendChild(panel);
    }

    // 初始化
    async function init() {
        if (isInitialized) return;
        
        try {
            console.log('PSD导出工具初始化中...');
            
            // 等待ag-psd库加载
            let attempts = 0;
            while (!window.agPsd && attempts < 50) {
                await new Promise(resolve => setTimeout(resolve, 100));
                attempts++;
            }
            
            if (!window.agPsd) {
                throw new Error('PSD库加载失败');
            }
            
            await waitForElement('canvas');
            
            setTimeout(() => {
                createExportButtons();
                showMessage('🎉 PSD导出工具已就绪！\n支持真正的PSD格式导出', 'success');
                isInitialized = true;
            }, 2000);
            
        } catch (error) {
            console.error('初始化失败:', error);
            showMessage('❌ 初始化失败:\n' + error.message + '\n请刷新页面重试', 'error');
        }
    }

    // 启动
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    console.log('稿定设计PSD导出工具(修复版)已加载');

})();
