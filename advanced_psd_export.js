// ==UserScript==
// @name         稿定设计PSD导出工具
// @namespace    http://tampermonkey.net/
// @version      2.0.0
// @description  为稿定设计网站添加PSD导出功能，自动检测画板尺寸和图层元素，支持多图层、文字图层、位置保持、透明度等，生成可编辑的PSD文件。
// <AUTHOR>
// @match        https://www.gaoding.com/editor/design*
// @icon         https://www.gaoding.com/favicon.ico
// @grant        GM_xmlhttpRequest
// @grant        GM_addStyle
// @grant        unsafeWindow
// @connect      *
// @require      https://cdn.jsdelivr.net/npm/ag-psd/dist/bundle.js
// @require      https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js
// ==/UserScript==

(function() {
    'use strict';

    // ========================
    // 常量和配置
    // ========================
    const CONFIG = {
        debug: false, // 关闭调试模式
        exportQuality: 1.0, // 图层导出质量 (0.0-1.0)
        defaultPsdName: '稿定设计导出',
        uiElements: {
            buttonId: 'gd-psd-export-button',
            modalId: 'gd-psd-export-modal',
            progressBarId: 'gd-psd-export-progress'
        },
        selectors: {
            canvas: '.editor-canvas-container canvas',
            layersPanel: '.editor-layers-panel',
            layerItem: '.layer-item',
            layerName: '.layer-name',
            textLayer: '.text-layer',
            imageLayer: '.image-layer'
        }
    };

    // ========================
    // 日志系统
    // ========================
    const logger = {
        log: function(message, ...args) {
            if (CONFIG.debug) {
                console.log(`[PSD导出] ${message}`, ...args);
            }
        },
        info: function(message, ...args) {
            if (CONFIG.debug) {
                console.info(`[PSD导出] ${message}`, ...args);
            }
        },
        warn: function(message, ...args) {
            if (CONFIG.debug) {
                console.warn(`[PSD导出] ${message}`, ...args);
            }
        },
        error: function(message, ...args) {
            if (CONFIG.debug) {
                console.error(`[PSD导出] ${message}`, ...args);
            }
        }
    };


    // ========================
    // 用户界面相关
    // ========================
    
    // 添加CSS样式
    GM_addStyle(`
        /* 导出按钮样式 (悬浮按钮) */
        #${CONFIG.uiElements.buttonId} {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background-color: #2878ff;
            color: white;
            border: none;
            border-radius: 50px;
            padding: 12px 18px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            z-index: 99999;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.25);
        }
        
        #${CONFIG.uiElements.buttonId}:hover {
            background-color: #4a90fe;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
        }
        


        #${CONFIG.uiElements.buttonId}.exporting {
            background-color: #cccccc;
            cursor: wait;
            transform: translateY(0);
        }
        
        /* 导出模态框样式 */
        #${CONFIG.uiElements.modalId} {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            padding: 20px;
            z-index: 10000;
            width: 400px;
            display: none;
        }
        
        #${CONFIG.uiElements.modalId} h3 {
            margin-top: 0;
            color: #333;
        }
        
        #${CONFIG.uiElements.modalId} .content {
            margin: 15px 0;
        }
        
        #${CONFIG.uiElements.modalId} .actions {
            display: flex;
            justify-content: flex-end;
            margin-top: 20px;
        }
        
        #${CONFIG.uiElements.modalId} button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 10px;
        }
        
        #${CONFIG.uiElements.modalId} button.cancel {
            background-color: #f5f5f5;
            color: #666;
        }
        
        #${CONFIG.uiElements.modalId} button.confirm {
            background-color: #2878ff;
            color: white;
        }
        
        /* 进度条样式 */
        #${CONFIG.uiElements.progressBarId} {
            width: 100%;
            height: 6px;
            background-color: #f0f0f0;
            border-radius: 3px;
            margin-top: 15px;
            overflow: hidden;
        }
        
        #${CONFIG.uiElements.progressBarId} .bar {
            height: 100%;
            background-color: #2878ff;
            width: 0;
            transition: width 0.3s ease;
        }
        
        .modal-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 9999;
            display: none;
        }
    `);

    // 创建导出按钮
    const createExportButton = () => {
        const button = document.createElement('button');
        button.id = CONFIG.uiElements.buttonId;
        button.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 6px;">
                <path d="M11.87 4.14a1 1 0 0 0-1.41 0L8.75 5.83V1a1 1 0 0 0-2 0v4.83L5.04 4.14a1 1 0 0 0-1.41 1.41l3.66 3.66c.2.2.45.29.7.29.25 0 .5-.1.7-.29l3.66-3.66a1 1 0 0 0 0-1.41z"/>
                <path d="M12.5 8.5a1 1 0 0 0-1 1V13H3V9.5a1 1 0 0 0-2 0V14a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1V9.5a1 1 0 0 0-1-1z"/>
            </svg>
            导出PSD
        `;
        button.addEventListener('click', handleExportButtonClick);
        return button;
    };
    


    // 创建模态框
    const createModal = () => {
        // 检查是否已存在
        if (document.getElementById(CONFIG.uiElements.modalId)) {
            return;
        }
        
        // 创建背景遮罩
        const backdrop = document.createElement('div');
        backdrop.className = 'modal-backdrop';
        document.body.appendChild(backdrop);
        
        // 创建模态框
        const modal = document.createElement('div');
        modal.id = CONFIG.uiElements.modalId;
        modal.innerHTML = `
            <h3>导出PSD文件</h3>
            <div class="content">
                <p>将导出当前设计为可编辑的PSD文件，保留图层和文字。</p>
                <div class="options">
                    <label>
                        <input type="checkbox" id="keep-layers" checked />
                        保持图层结构
                    </label>
                    <br />
                    <label>
                        <input type="checkbox" id="export-text-as-text" checked />
                        导出文字为可编辑文字层
                    </label>
                </div>
                <div id="${CONFIG.uiElements.progressBarId}" style="display: none;">
                    <div class="bar"></div>
                </div>
            </div>
            <div class="actions">
                <button class="cancel">取消</button>
                <button class="confirm">导出</button>
            </div>
        `;
        document.body.appendChild(modal);
        
        // 绑定事件
        modal.querySelector('button.cancel').addEventListener('click', hideModal);
        modal.querySelector('button.confirm').addEventListener('click', startExport);
        backdrop.addEventListener('click', hideModal);
        
        return modal;
    };

    // 显示模态框
    const showModal = () => {
        const modal = document.getElementById(CONFIG.uiElements.modalId) || createModal();
        modal.style.display = 'block';
        document.querySelector('.modal-backdrop').style.display = 'block';
    };

    // 隐藏模态框
    const hideModal = () => {
        const modal = document.getElementById(CONFIG.uiElements.modalId);
        if (modal) {
            modal.style.display = 'none';
        }
        const backdrop = document.querySelector('.modal-backdrop');
        if (backdrop) {
            backdrop.style.display = 'none';
        }
    };

    // 更新进度条
    const updateProgress = (percent) => {
        const progressBar = document.querySelector(`#${CONFIG.uiElements.progressBarId} .bar`);
        if (progressBar) {
            progressBar.style.width = `${percent}%`;
        }
    };

    // 显示进度条
    const showProgress = () => {
        const progressBar = document.getElementById(CONFIG.uiElements.progressBarId);
        if (progressBar) {
            progressBar.style.display = 'block';
            updateProgress(0);
        }
    };

    // 隐藏进度条
    const hideProgress = () => {
        const progressBar = document.getElementById(CONFIG.uiElements.progressBarId);
        if (progressBar) {
            progressBar.style.display = 'none';
        }
    };

    // ========================
    // 核心导出功能
    // ========================

    // ========================
    // 画板尺寸检测功能
    // ========================
    const detectCanvasSize = () => {
        logger.log('开始检测画板尺寸...');

        // 策略1: 从UI元素中获取尺寸信息
        const sizeInfoElements = Array.from(document.querySelectorAll(
            '.editor-size-info, .size-info, [class*="size-info"], [class*="canvas-size"], [class*="design-size"], .canvas-info'
        ));

        for (const el of sizeInfoElements) {
            const text = el.textContent || el.innerText;
            const match = text.match(/(\d+)\s*[×x]\s*(\d+)/);
            if (match) {
                const width = parseInt(match[1], 10);
                const height = parseInt(match[2], 10);
                if (width > 0 && height > 0) {
                    logger.log(`从UI元素获取到画板尺寸: ${width}x${height}`);
                    return { width, height };
                }
            }
        }

        // 策略2: 从Canvas元素属性中获取
        const canvasElements = document.querySelectorAll('canvas');
        for (const canvas of canvasElements) {
            if (canvas.width > 100 && canvas.height > 100) {
                const dataWidth = canvas.getAttribute('data-width') || canvas.getAttribute('data-original-width');
                const dataHeight = canvas.getAttribute('data-height') || canvas.getAttribute('data-original-height');

                if (dataWidth && dataHeight) {
                    const width = parseInt(dataWidth, 10);
                    const height = parseInt(dataHeight, 10);
                    if (width > 0 && height > 0) {
                        logger.log(`从Canvas属性获取到画板尺寸: ${width}x${height}`);
                        return { width, height };
                    }
                }
            }
        }

        // 策略3: 从设计容器元素的样式中获取
        const designContainers = document.querySelectorAll(
            '.editor-board, .editor-stage, .editor-design-board, .canvas-container, .editor-content, .design-content'
        );

        for (const container of designContainers) {
            const computedStyle = window.getComputedStyle(container);
            const width = parseInt(computedStyle.width, 10);
            const height = parseInt(computedStyle.height, 10);

            if (width > 100 && height > 100) {
                logger.log(`从设计容器样式获取到画板尺寸: ${width}x${height}`);
                return { width, height };
            }
        }

        logger.warn('无法检测到画板尺寸，使用默认值');
        return { width: 800, height: 600 }; // 默认尺寸
    };

    const extractFullDesignData = async () => {
        logger.log('开始采用【精确制导】多策略扫描获取设计数据...');

        const visited = new Set();

        function isElementLike(obj) {
            if (!obj || typeof obj !== 'object') return false;
            const hasType = typeof obj.type === 'string' && obj.type;
            const hasTransform = obj.hasOwnProperty('left') && obj.hasOwnProperty('top');
            const hasSize = obj.hasOwnProperty('width') && obj.hasOwnProperty('height');
            return hasType && hasTransform && hasSize;
        }

        function findDataRecursive(currentObj) {
            if (!currentObj || typeof currentObj !== 'object' || visited.has(currentObj)) {
                return null;
            }
            visited.add(currentObj);

            if (Array.isArray(currentObj.elements) && currentObj.elements.length > 0 &&
                typeof currentObj.width === 'number' && currentObj.width > 0 &&
                typeof currentObj.height === 'number' && currentObj.height > 0 &&
                isElementLike(currentObj.elements[0])) {

                const elements = currentObj.elements;
                if (elements.every(isElementLike)) {
                    logger.log('深度扫描匹配成功！父级对象:', currentObj);
                    return {
                        elements: elements,
                        width: currentObj.width,
                        height: currentObj.height,
                        background: currentObj.background // 增强: 同时提取背景信息
                    };
                }
            }

            for (const key in currentObj) {
                if (!currentObj.hasOwnProperty(key)) continue;
                if (['self', 'window', 'document', 'top', 'webpackChunk_N_E'].includes(key)) continue;

                try {
                    const prop = currentObj[key];
                    if (prop && typeof prop === 'object') {
                        const result = findDataRecursive(prop);
                        if (result) return result;
                    }
                } catch (e) { /* Ignore access errors */ }
            }
            return null;
        }
        
        function flattenElements(elements, parentLeft = 0, parentTop = 0) {
            let flatList = [];
            for (const el of elements) {
                const absoluteLeft = parentLeft + (el.left || 0);
                const absoluteTop = parentTop + (el.top || 0);

                if (el.type === 'group' && Array.isArray(el.elements)) {
                    // 递归处理组元素
                    flatList = flatList.concat(flattenElements(el.elements, absoluteLeft, absoluteTop));
                } else {
                    // 生成唯一ID
                    const id = el.id || `el-${Math.random().toString(36).slice(2, 11)}`;

                    // 增强元素类型检测
                    let elementType = el.type;
                    if (!elementType) {
                        // 根据元素属性推断类型
                        if (el.url || el.src) {
                            elementType = 'image';
                        } else if (el.content || el.text) {
                            elementType = 'text';
                        } else if (el.svg) {
                            elementType = 'svg';
                        } else {
                            elementType = 'unknown';
                        }
                    }

                    // 增强透明度处理
                    let opacity = 1;
                    if (typeof el.opacity === 'number') {
                        opacity = el.opacity;
                    } else if (typeof el.alpha === 'number') {
                        opacity = el.alpha;
                    } else if (el.style && typeof el.style.opacity === 'number') {
                        opacity = el.style.opacity;
                    }

                    // 增强尺寸处理
                    const width = el.width || el.w || 0;
                    const height = el.height || el.h || 0;

                    // 只处理有效的元素（有尺寸的）
                    if (width > 0 && height > 0) {
                        const formattedEl = {
                            id: id,
                            name: el.name || el.title || `${elementType}-${id}`,
                            type: elementType,
                            visible: el.visible !== false && el.display !== 'none',
                            left: absoluteLeft,
                            top: absoluteTop,
                            width: width,
                            height: height,
                            opacity: opacity,
                            rotation: (el.transform && el.transform.rotation) || el.rotation || 0,
                            raw: el
                        };
                        flatList.push(formattedEl);
                        logger.log(`检测到${elementType}元素: ${formattedEl.name} (${width}x${height})`);
                    }
                }
            }
            return flatList;
        }

        // --- 最终方案 ---

        // 策略1: 精确打击用户指定的目标元素
        const targetSelectors = ['.editor-board', '.editor-stage'];
        for (const selector of targetSelectors) {
            const targetElement = document.querySelector(selector);
            if (targetElement) {
                logger.log(`找到目标容器元素: "${selector}"`, targetElement);
                for (const key in targetElement) {
                    if (key.startsWith('__')) { // 前端框架属性通常以'__'开头
                        logger.log(`检查目标元素的属性 [${key}]...`);
                        try {
                            const dataSource = targetElement[key];
                            if (dataSource && typeof dataSource === 'object') {
                                visited.clear(); // 为每次新的根搜索重置 visited
                                const found = findDataRecursive(dataSource);
                                if (found) {
                                    const flattened = flattenElements(found.elements);
                                    logger.log(`在 "${selector}" 的属性 [${key}] 中找到设计数据！`);
                                    return { ...found, elements: flattened }; // 增强: 传递包括背景在内的完整数据
                                }
                            }
                        } catch (e) { logger.warn(`检查属性 ${key} 时出错`, e) }
                    }
                }
            }
        }
        
        // 策略2: 全局扫描作为备用方案
        logger.warn('在指定元素上未找到数据，回退到全局扫描...');
        visited.clear();
        try {
            const foundGlobal = findDataRecursive(unsafeWindow);
            if (foundGlobal) {
                const flattened = flattenElements(foundGlobal.elements);
                logger.log(`全局扫描成功！`);
                return { ...foundGlobal, elements: flattened }; // 增强: 传递包括背景在内的完整数据
            }
        } catch (error) {
            logger.error('全局深度扫描时发生严重错误', error);
        }

        // 策略3: 如果无法找到设计数据，至少获取画板尺寸
        logger.warn('无法找到完整设计数据，尝试获取画板尺寸...');
        const canvasSize = detectCanvasSize();

        if (canvasSize.width > 0 && canvasSize.height > 0) {
            logger.log('成功获取画板尺寸，返回基础设计数据');
            return {
                elements: [], // 空的元素数组
                width: canvasSize.width,
                height: canvasSize.height,
                background: '#FFFFFF' // 默认白色背景
            };
        }

        logger.error('【根本性失败】所有方案均未能找到任何有效的设计数据。无法进行分层导出。');
        return null;
    };

    // ========================
    // 图像加载工具 (支持会员权限)
    // ========================
    const loadImage = (url) => {
        return new Promise((resolve, reject) => {
            if (!url) return reject(new Error('Image URL is empty.'));
            if (!url.startsWith('http')) { url = 'https:' + url; }

            const img = new Image();
            img.crossOrigin = "anonymous";
            img.onload = () => resolve(img);
            img.onerror = () => {
                logger.warn(`常规Image加载失败: ${url}, 尝试GM_xmlhttpRequest代理...`);
                // 备用方案: 使用GM_xmlhttpRequest获取blob，然后创建URL。
                // 这会携带用户Cookie，可用于下载会员素材。
                GM_xmlhttpRequest({
                    method: 'GET',
                    url: url,
                    responseType: 'blob',
                    headers: {
                        'Referer': 'https://www.gaoding.com/' // 伪装来源
                    },
                    onload: function(response) {
                        const blobUrl = URL.createObjectURL(response.response);
                        const proxiedImg = new Image();
                        proxiedImg.onload = () => {
                            URL.revokeObjectURL(blobUrl);
                            resolve(proxiedImg);
                        };
                        proxiedImg.onerror = () => {
                             URL.revokeObjectURL(blobUrl);
                             reject(new Error(`代理Image加载也失败: ${url}`));
                        }
                        proxiedImg.src = blobUrl;
                    },
                    onerror: function(error) {
                        reject(new Error(`GM_xmlhttpRequest failed for image: ${url}`, {cause: error}));
                    }
                });
            };
            img.src = url;
        });
    };



    // 创建PSD文件数据
    const createPSDFile = async (designData, layers) => {
        try {
            logger.log('开始使用 ag-psd 创建PSD文件', {
                canvasSize: `${designData.width}x${designData.height}`,
                layersCount: layers.length
            });
            showProgress();

            // 创建主画布作为背景
            const mainCanvas = document.createElement('canvas');
            mainCanvas.width = designData.width;
            mainCanvas.height = designData.height;
            const mainCtx = mainCanvas.getContext('2d');

            // 根据背景设置填充颜色
            const backgroundColor = designData.background || '#FFFFFF';
            mainCtx.fillStyle = backgroundColor;
            mainCtx.fillRect(0, 0, designData.width, designData.height);

            // 创建符合ag-psd格式的PSD结构
            const psd = {
                width: designData.width,
                height: designData.height,
                channels: 3,
                bitsPerChannel: 8,
                colorMode: 3, // RGB
                canvas: mainCanvas, // 主画布
                children: []
            };
            updateProgress(10);

            // 如果没有图层，至少创建一个背景图层
            if (!layers || layers.length === 0) {
                logger.warn('没有检测到图层，创建背景图层');
                psd.children.push({
                    name: '背景',
                    left: 0,
                    top: 0,
                    right: designData.width,
                    bottom: designData.height,
                    opacity: 255,
                    blendMode: 'normal',
                    canvas: mainCanvas
                });
                updateProgress(100);
                const buffer = window.agPsd.writePsd(psd);
                return new Blob([buffer], { type: 'application/vnd.adobe.photoshop' });
            }

            const reversedLayers = [...layers].reverse();
            let processedLayers = 0;

            for (const [index, layer] of reversedLayers.entries()) {
                const progress = 10 + Math.floor(((index + 1) / reversedLayers.length) * 80);
                updateProgress(progress);

                if (!layer.visible) {
                    logger.log(`跳过不可见图层: "${layer.name}"`);
                    continue;
                }

                // 修复透明度处理：确保在0-255范围内
                const layerOpacity = Math.max(0, Math.min(255, Math.round((typeof layer.opacity === 'number' ? layer.opacity : 1) * 255)));

                // 处理图片图层
                if (layer.type === 'image') {
                    const imageUrl = layer.raw.url || layer.raw.src;
                    if (imageUrl) {
                        try {
                            logger.log(`处理图片图层: "${layer.name}"`, imageUrl);

                            // 直接加载图片
                            const img = await loadImage(imageUrl);

                            // 创建图层画布
                            const layerCanvas = document.createElement('canvas');
                            layerCanvas.width = layer.width;
                            layerCanvas.height = layer.height;
                            const layerCtx = layerCanvas.getContext('2d');

                            // 绘制图片到画布
                            layerCtx.drawImage(img, 0, 0, layer.width, layer.height);

                            // 获取图像数据用于PSD
                            const imageData = layerCtx.getImageData(0, 0, layer.width, layer.height);

                            psd.children.push({
                                name: layer.name,
                                left: layer.left,
                                top: layer.top,
                                right: layer.left + layer.width,
                                bottom: layer.top + layer.height,
                                opacity: layerOpacity,
                                blendMode: 'normal',
                                canvas: layerCanvas,
                                imageData: imageData
                            });

                            // 将图层绘制到主画布上
                            mainCtx.globalAlpha = layerOpacity / 255;
                            mainCtx.drawImage(layerCanvas, layer.left, layer.top);
                            mainCtx.globalAlpha = 1.0;

                            processedLayers++;
                            logger.log(`图片图层 "${layer.name}" 处理成功`);
                        } catch (e) {
                            logger.error(`处理图片图层 "${layer.name}" 失败`, e);
                        }
                    } else {
                        logger.warn(`图片图层 "${layer.name}" 缺少图片URL`);
                    }
                } else if (layer.type === 'svg') {
                    const svgContent = layer.raw.svg || layer.raw.content;
                    if (svgContent) {
                        try {
                            logger.log(`处理SVG图层: "${layer.name}"`);

                            // 创建SVG图片
                            const img = new Image();
                            const svgBlob = new Blob([svgContent], { type: 'image/svg+xml' });
                            const svgUrl = URL.createObjectURL(svgBlob);

                            await new Promise((resolve, reject) => {
                                img.onload = resolve;
                                img.onerror = reject;
                                img.src = svgUrl;
                            });

                            // 创建图层画布
                            const layerCanvas = document.createElement('canvas');
                            layerCanvas.width = layer.width;
                            layerCanvas.height = layer.height;
                            const layerCtx = layerCanvas.getContext('2d');

                            // 绘制SVG到画布
                            layerCtx.drawImage(img, 0, 0, layer.width, layer.height);
                            URL.revokeObjectURL(svgUrl);

                            // 获取图像数据
                            const imageData = layerCtx.getImageData(0, 0, layer.width, layer.height);

                            psd.children.push({
                                name: layer.name,
                                left: layer.left,
                                top: layer.top,
                                right: layer.left + layer.width,
                                bottom: layer.top + layer.height,
                                opacity: layerOpacity,
                                blendMode: 'normal',
                                canvas: layerCanvas,
                                imageData: imageData
                            });

                            // 将图层绘制到主画布上
                            mainCtx.globalAlpha = layerOpacity / 255;
                            mainCtx.drawImage(layerCanvas, layer.left, layer.top);
                            mainCtx.globalAlpha = 1.0;

                            processedLayers++;
                            logger.log(`SVG图层 "${layer.name}" 处理成功`);
                        } catch(e) {
                            logger.error(`处理SVG图层 "${layer.name}" 失败`, e);
                        }
                    } else {
                        logger.warn(`跳过无内容的SVG图层: "${layer.name}"`);
                    }
                // 处理文本图层
                } else if (layer.type === 'text') {
                    try {
                        logger.log(`处理文本图层: "${layer.name}"`);
                        const text = layer.raw;
                        const font = text.fontStyle || text.style || {};

                        // 增强文本内容提取
                        const textContent = text.content || text.text || text.value || layer.name || '';
                        if (!textContent) {
                            logger.warn(`文本图层 "${layer.name}" 没有文本内容，跳过`);
                            continue;
                        }

                        // 增强字体属性提取
                        const fontSize = font.fontSize || font.size || text.fontSize || 16;
                        const fontFamily = font.fontFamily || font.family || text.fontFamily || 'Arial, sans-serif';
                        const fontWeight = font.fontWeight || font.weight || text.fontWeight || 'normal';
                        const textAlign = font.textAlign || font.align || text.textAlign || 'left';

                        // 增强颜色信息提取
                        let r = 0, g = 0, b = 0, a = 255;

                        // 尝试多种颜色提取方式
                        if (text.fills && Array.isArray(text.fills)) {
                            const mainFill = text.fills.find(f => f.isEnabled !== false);
                            if (mainFill && mainFill.color && mainFill.color.rgba) {
                                const c = mainFill.color.rgba;
                                r = Math.round((c.r || 0) * 255);
                                g = Math.round((c.g || 0) * 255);
                                b = Math.round((c.b || 0) * 255);
                                a = Math.round((c.a || 1) * 255);
                            }
                        } else if (text.color) {
                            if (text.color.rgba) {
                                const c = text.color.rgba;
                                r = Math.round((c.r || 0) * 255);
                                g = Math.round((c.g || 0) * 255);
                                b = Math.round((c.b || 0) * 255);
                                a = Math.round((c.a || 1) * 255);
                            } else if (typeof text.color === 'string') {
                                const hexMatch = text.color.match(/#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})/i);
                                if (hexMatch) {
                                    r = parseInt(hexMatch[1], 16);
                                    g = parseInt(hexMatch[2], 16);
                                    b = parseInt(hexMatch[3], 16);
                                    a = 255;
                                }
                            }
                        } else if (font.color) {
                            // 从字体样式中提取颜色
                            if (typeof font.color === 'string') {
                                const hexMatch = font.color.match(/#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})/i);
                                if (hexMatch) {
                                    r = parseInt(hexMatch[1], 16);
                                    g = parseInt(hexMatch[2], 16);
                                    b = parseInt(hexMatch[3], 16);
                                    a = 255;
                                }
                            }
                        }

                        // 确定文本对齐方式
                        let alignmentValue = 0; // 默认左对齐
                        if (textAlign === 'center') {
                            alignmentValue = 1;
                        } else if (textAlign === 'right') {
                            alignmentValue = 2;
                        }

                        // 创建文本图层 - 使用ag-psd的文本格式
                        psd.children.push({
                            name: layer.name,
                            left: layer.left,
                            top: layer.top,
                            right: layer.left + layer.width,
                            bottom: layer.top + layer.height,
                            opacity: layerOpacity,
                            blendMode: 'normal',
                            text: {
                                text: textContent,
                                font: {
                                    name: fontFamily,
                                    size: fontSize,
                                    color: { r, g, b, a },
                                    weight: fontWeight === 'bold' ? 700 : 400
                                },
                                alignment: alignmentValue
                            }
                        });

                        // 同时创建文本的canvas版本用于主画布合成
                        const textCanvas = document.createElement('canvas');
                        textCanvas.width = layer.width;
                        textCanvas.height = layer.height;
                        const textCtx = textCanvas.getContext('2d');

                        // 设置字体样式
                        const fontStyle = fontWeight === 'bold' ? 'bold' : 'normal';
                        textCtx.font = `${fontStyle} ${fontSize}px ${fontFamily}`;
                        textCtx.fillStyle = `rgba(${r}, ${g}, ${b}, ${a/255})`;
                        textCtx.textAlign = textAlign;
                        textCtx.textBaseline = 'top';

                        // 处理多行文本
                        const lines = textContent.split('\n');
                        const lineHeight = fontSize * 1.2;

                        // 根据对齐方式调整文本位置
                        lines.forEach((line, lineIndex) => {
                            let xPos = 0;
                            if (textAlign === 'center') {
                                xPos = layer.width / 2;
                            } else if (textAlign === 'right') {
                                xPos = layer.width;
                            }
                            textCtx.fillText(line, xPos, lineIndex * lineHeight);
                        });

                        // 将文字图层绘制到主画布上
                        mainCtx.globalAlpha = layerOpacity / 255;
                        mainCtx.drawImage(textCanvas, layer.left, layer.top);
                        mainCtx.globalAlpha = 1.0;

                        processedLayers++;
                        logger.log(`文本图层 "${layer.name}" 处理成功: "${textContent.substring(0, 20)}${textContent.length > 20 ? '...' : ''}"`);
                    } catch (e) {
                        logger.error(`处理文本图层 "${layer.name}" 失败`, e);
                    }
                // 处理其他类型的图层
                } else {
                    // 尝试处理未知类型的图层
                    try {
                        logger.log(`尝试处理未知类型图层: "${layer.type}" - "${layer.name}"`);

                        // 检查是否有图片URL
                        const imageUrl = layer.raw.url || layer.raw.src || layer.raw.image;
                        if (imageUrl) {
                            logger.log(`未知类型图层包含图片URL，按图片处理: ${imageUrl}`);
                            const img = await loadImage(imageUrl);

                            const layerCanvas = document.createElement('canvas');
                            layerCanvas.width = layer.width;
                            layerCanvas.height = layer.height;
                            const layerCtx = layerCanvas.getContext('2d');

                            layerCtx.drawImage(img, 0, 0, layer.width, layer.height);
                            const imageData = layerCtx.getImageData(0, 0, layer.width, layer.height);

                            psd.children.push({
                                name: layer.name,
                                left: layer.left,
                                top: layer.top,
                                right: layer.left + layer.width,
                                bottom: layer.top + layer.height,
                                opacity: layerOpacity,
                                blendMode: 'normal',
                                canvas: layerCanvas,
                                imageData: imageData
                            });

                            mainCtx.globalAlpha = layerOpacity / 255;
                            mainCtx.drawImage(layerCanvas, layer.left, layer.top);
                            mainCtx.globalAlpha = 1.0;

                            processedLayers++;
                            logger.log(`未知类型图层 "${layer.name}" 按图片处理成功`);
                        } else {
                            // 创建一个占位符图层
                            const placeholderCanvas = document.createElement('canvas');
                            placeholderCanvas.width = layer.width;
                            placeholderCanvas.height = layer.height;
                            const placeholderCtx = placeholderCanvas.getContext('2d');

                            // 绘制半透明的占位符
                            placeholderCtx.fillStyle = 'rgba(200, 200, 200, 0.5)';
                            placeholderCtx.fillRect(0, 0, layer.width, layer.height);
                            placeholderCtx.strokeStyle = 'rgba(100, 100, 100, 0.8)';
                            placeholderCtx.strokeRect(0, 0, layer.width, layer.height);

                            psd.children.push({
                                name: layer.name + ' (占位符)',
                                left: layer.left,
                                top: layer.top,
                                right: layer.left + layer.width,
                                bottom: layer.top + layer.height,
                                opacity: layerOpacity,
                                blendMode: 'normal',
                                canvas: placeholderCanvas
                            });

                            mainCtx.globalAlpha = layerOpacity / 255;
                            mainCtx.drawImage(placeholderCanvas, layer.left, layer.top);
                            mainCtx.globalAlpha = 1.0;

                            processedLayers++;
                            logger.log(`未知类型图层 "${layer.name}" 创建占位符成功`);
                        }
                    } catch (e) {
                        logger.error(`处理未知类型图层 "${layer.name}" 失败`, e);
                    }
                }
            }

            updateProgress(95);

            // 确保至少有一个图层（背景）
            if (psd.children.length === 0) {
                logger.warn('没有处理任何图层，添加背景图层');
                psd.children.push({
                    name: '背景',
                    left: 0,
                    top: 0,
                    right: designData.width,
                    bottom: designData.height,
                    opacity: 255,
                    blendMode: 'normal',
                    canvas: mainCanvas
                });
            }

            logger.log(`PSD结构完成，共处理 ${processedLayers} 个图层，总图层数: ${psd.children.length}`);
            logger.log('准备写入PSD数据...', {
                width: psd.width,
                height: psd.height,
                layerCount: psd.children.length,
                layers: psd.children.map(l => ({ name: l.name, opacity: l.opacity }))
            });

            const buffer = window.agPsd.writePsd(psd);
            updateProgress(100);

            return new Blob([buffer], { type: 'application/vnd.adobe.photoshop' });

        } catch (error) {
            logger.error('使用 ag-psd 创建PSD文件失败', error);
            hideProgress();
            throw error;
        }
    };



    // ========================
    // 事件处理
    // ========================
    
    // 导出按钮点击处理
    const handleExportButtonClick = (event) => {
        event.preventDefault();
        showModal();
    };




    // 开始导出流程
    const startExport = async () => {
        try {
            // 更新UI状态
            const exportButton = document.getElementById(CONFIG.uiElements.buttonId);
            if (exportButton) {
                exportButton.classList.add('exporting');
                exportButton.innerHTML = '导出中...';
            }
            
            // 显示进度条
            showProgress();
            updateProgress(5);
            
            logger.log('开始深度扫描以提取设计数据...');
            const designData = await extractFullDesignData();

            if (!designData || designData.elements.length === 0) {
                alert('导出失败：无法从页面中提取到有效的设计数据。请刷新页面或联系脚本作者。');
                hideModal();
                // 在这里重置按钮状态
                if (exportButton) {
                    exportButton.classList.remove('exporting');
                    exportButton.innerHTML = `
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 6px;">
                            <path d="M11.87 4.14a1 1 0 0 0-1.41 0L8.75 5.83V1a1 1 0 0 0-2 0v4.83L5.04 4.14a1 1 0 0 0-1.41 1.41l3.66 3.66c.2.2.45.29.7.29.25 0 .5-.1.7-.29l3.66-3.66a1 1 0 0 0 0-1.41z"/>
                            <path d="M12.5 8.5a1 1 0 0 0-1 1V13H3V9.5a1 1 0 0 0-2 0V14a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1V9.5a1 1 0 0 0-1-1z"/>
                        </svg>
                        导出PSD
                    `;
                }
                return;
            }
            updateProgress(30);
            
            // 创建PSD文件
            logger.log('创建PSD文件...');
            const psdData = await createPSDFile(designData, designData.elements);
            
            // 保存PSD文件
            const filename = `${CONFIG.defaultPsdName}_${new Date().getTime()}.psd`;
            saveAs(psdData, filename);
            
            // 完成处理
            logger.info('PSD文件导出成功!', filename);
            hideModal();
            
            // 重置UI
            if (exportButton) {
                exportButton.classList.remove('exporting');
                exportButton.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 6px;">
                        <path d="M11.87 4.14a1 1 0 0 0-1.41 0L8.75 5.83V1a1 1 0 0 0-2 0v4.83L5.04 4.14a1 1 0 0 0-1.41 1.41l3.66 3.66c.2.2.45.29.7.29.25 0 .5-.1.7-.29l3.66-3.66a1 1 0 0 0 0-1.41z"/>
                        <path d="M12.5 8.5a1 1 0 0 0-1 1V13H3V9.5a1 1 0 0 0-2 0V14a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1V9.5a1 1 0 0 0-1-1z"/>
                    </svg>
                    导出PSD
                `;
            }
            
            // 删除烦人的确认弹窗
            // 显示成功消息
            // alert(`PSD文件 "${filename}" 已成功导出！`);
            
        } catch (error) {
            logger.error('导出PSD文件失败', error);
            alert(`导出失败: ${error.message}`);
            
            // 重置UI
            const exportButton = document.getElementById(CONFIG.uiElements.buttonId);
            if (exportButton) {
                exportButton.classList.remove('exporting');
                exportButton.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 6px;">
                        <path d="M11.87 4.14a1 1 0 0 0-1.41 0L8.75 5.83V1a1 1 0 0 0-2 0v4.83L5.04 4.14a1 1 0 0 0-1.41 1.41l3.66 3.66c.2.2.45.29.7.29.25 0 .5-.1.7-.29l3.66-3.66a1 1 0 0 0 0-1.41z"/>
                        <path d="M12.5 8.5a1 1 0 0 0-1 1V13H3V9.5a1 1 0 0 0-2 0V14a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1V9.5a1 1 0 0 0-1-1z"/>
                    </svg>
                    导出PSD
                `;
            }
            
            hideModal();
        }
    };

    // ========================
    // 初始化和安装
    // ========================
    
    // 插入导出按钮
    const insertButtons = () => {
        // 检查按钮是否已存在
        if (document.getElementById(CONFIG.uiElements.buttonId)) {
            return;
        }
        
        // 创建并插入按钮到 body
        const exportButton = createExportButton();
        document.body.appendChild(exportButton);
        logger.log('导出按钮已作为悬浮按钮添加到页面');
    };
    
    // 脚本初始化
    const init = () => {
        logger.info('稿定设计PSD导出工具初始化');
        
        // 检查依赖库
        if (typeof agPsd === 'undefined' || typeof saveAs === 'undefined' || typeof html2canvas === 'undefined') {
            logger.error('依赖库未加载，脚本无法运行');
            alert('稿定设计PSD导出工具需要的依赖库 ag-psd 或 FileSaver 或 html2canvas 未加载，请检查脚本设置或刷新页面重试');
            return;
        }

        // 页面加载完成后插入按钮
        if (document.readyState === 'complete' || document.readyState === 'interactive') {
            insertButtons();
        } else {
            window.addEventListener('load', insertButtons);
        }
    };
    
    // 启动脚本
    init();
})(); 